ifndef TARGET_COMPILE
    TARGET_COMPILE := D:/arm-gnu-toolchain-14.3.rel1-mingw-w64-x86_64-aarch64-none-elf/bin/aarch64-none-elf-
endif

ifndef KP_DIR
    KP_DIR = ..
endif

# 输出目录
BUILD_DIR := kpm

CC = $(TARGET_COMPILE)gcc
LD = $(TARGET_COMPILE)ld

INCLUDE_DIRS := . include patch/include linux/include linux/arch/arm64/include linux/tools/arch/arm64/include

INCLUDE_FLAGS := $(foreach dir,$(INCLUDE_DIRS),-I$(KP_DIR)/kernel/$(dir))

# 源文件
SOURCES := main.c mincore_hook.c so_hide.c block_mrpcs.c anti_debug.c
# 对象文件（放到 BUILD_DIR 中）
OBJS := $(addprefix $(BUILD_DIR)/,$(SOURCES:.c=.o))

all: $(BUILD_DIR)/game_protection.kpm

# 创建输出目录
$(BUILD_DIR):
	@if not exist $(BUILD_DIR) mkdir $(BUILD_DIR)

# 生成最终的 kmp 文件
$(BUILD_DIR)/game_protection.kpm: $(OBJS) | $(BUILD_DIR)
	${CC} -r -o $@ $^

# 编译 .c 文件到 .o 文件
$(BUILD_DIR)/%.o: %.c | $(BUILD_DIR)
	${CC} $(CFLAGS) $(INCLUDE_FLAGS) -c -O2 -o $@ $<

.PHONY: clean
clean:
	@if exist $(BUILD_DIR) rmdir /s /q $(BUILD_DIR)
	@if exist *.o del *.o
	@if exist *.kpm del *.kpm