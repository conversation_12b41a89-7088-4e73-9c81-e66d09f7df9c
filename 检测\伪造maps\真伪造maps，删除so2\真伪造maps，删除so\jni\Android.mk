LOCAL_PATH := $(call my-dir)
MAIN_LOCAL_PATH := $(call my-dir)

# 预编译静态库
include $(CLEAR_VARS)
LOCAL_MODULE := libdobby
LOCAL_SRC_FILES := Dobby/libdobby.a
include $(PREBUILT_STATIC_LIBRARY)

# 主模块 - 直接用于替换UE5.so
include $(CLEAR_VARS)
LOCAL_MODULE    := UE5
LOCAL_CPPFLAGS := -g0 -O2 -w -fvisibility=hidden -fno-stack-protector -fms-extensions -s -DDBG=0 -DNDEBUG=1 -std=c++17
LOCAL_LDLIBS += -lc -lz -lm -llog -landroid -ldl

# 使用main.cpp作为主要源文件
LOCAL_SRC_FILES := main.cpp
LOCAL_STATIC_LIBRARIES := libdobby

# 允许未定义的符号，这样链接时不会报错
LOCAL_LDFLAGS += -Wl,--allow-shlib-undefined

include $(BUILD_SHARED_LIBRARY)