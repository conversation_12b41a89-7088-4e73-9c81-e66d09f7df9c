/* SPDX-License-Identifier: GPL-2.0-or-later */
/* 
 * 反调试检测绕过模块
 * 目标：让游戏无法检测到调试器的存在，允许正常调试
 * 原理：拦截游戏的反调试检测调用，伪造"无调试器"的环境
 */

#include <compiler.h>
#include <kpmodule.h>
#include <linux/printk.h>
#include <uapi/asm-generic/unistd.h>
#include <linux/uaccess.h>
#include <syscall.h>
#include <linux/string.h>
#include <kputils.h>

// 日志标签
#define ANTI_DEBUG_TAG "AntiDebug"

// 简单的计数器
static uint64_t ptrace_block_count = 0;
static uint64_t status_modify_count = 0;

// 1. Hook ptrace - 通杀反调试检测
void before_ptrace_hook(hook_fargs4_t *args, void *udata) {
    long request = (long)syscall_argn(args, 0);
    
    // 阻止常见的反调试 ptrace 调用
    if (request == 0) {  // PTRACE_TRACEME - 最常用的反调试检测
        pr_info("[%s] 伪造 PTRACE_TRACEME 成功（无调试器）", ANTI_DEBUG_TAG);
        args->skip_origin = 1;
        args->ret = 0;  // 🎯 伪造成功，让游戏以为没有调试器
        ptrace_block_count++;
    }
    else if (request == 16) {  // PTRACE_ATTACH - fork+ptrace自检测
        pr_info("[%s] 伪造 PTRACE_ATTACH 成功（无调试器）", ANTI_DEBUG_TAG);
        args->skip_origin = 1;
        args->ret = 0;  // 🎯 伪造attach成功，让游戏以为没有调试器
        ptrace_block_count++;
    }
    // 其他 ptrace 调用正常通过，不影响调试器
}

// 2. Hook read - 简化版 TracerPid 修改
void after_read_antidebug(hook_fargs3_t *args, void *udata) {
    char __user *buf = (char __user *)syscall_argn(args, 1);
    ssize_t bytes_read = (ssize_t)args->ret;
    char kernel_buf[512];  // 使用栈缓冲区，避免kmalloc
    char *tracer_pos;
    
    if (bytes_read <= 0 || bytes_read > sizeof(kernel_buf) - 1) return;  // 安全检查
    
    // 复制用户数据到内核检查
    if (compat_strncpy_from_user(kernel_buf, buf, bytes_read) < 0) {
        return;
    }
    kernel_buf[bytes_read] = '\0';
    
    // 检查是否包含 TracerPid（说明是 /proc/status）
    tracer_pos = strstr(kernel_buf, "TracerPid:");
    if (tracer_pos) {
        // 找到！直接替换为 TracerPid: 0
        char *line_end = strchr(tracer_pos, '\n');
        if (line_end) {
            // 简单粗暴：直接覆盖为固定格式
            memcpy(tracer_pos, "TracerPid:\t0\n", 13);
            
            // 写回用户空间
            if (compat_copy_to_user(buf, kernel_buf, bytes_read) == 0) {
                pr_info("[%s] TracerPid 已伪造为 0", ANTI_DEBUG_TAG);
                status_modify_count++;
            }
        }
    }
}

// 初始化反调试绕过 - 简化版
void init_anti_debug_hooks(void) {
    hook_err_t err;
    
    pr_info("[%s] 通杀反调试检测模块启动...", ANTI_DEBUG_TAG);
    
    // Hook ptrace - 阻止 PTRACE_TRACEME
    err = fp_hook_syscalln(__NR_ptrace, 4, before_ptrace_hook, 0, 0);
    if (err != HOOK_NO_ERR) {
        pr_err("[%s] Hook ptrace 失败：%d", ANTI_DEBUG_TAG, err);
        return;
    }
    
    // Hook read - 伪造 TracerPid
    err = fp_hook_syscalln(__NR_read, 3, 0, after_read_antidebug, 0);
    if (err != HOOK_NO_ERR) {
        pr_err("[%s] Hook read 失败：%d", ANTI_DEBUG_TAG, err);
        return;
    }
    
    pr_info("[%s] ✅ 通杀反调试模块加载完成", ANTI_DEBUG_TAG);
    pr_info("[%s] ✅ PTRACE_TRACEME 检测已屏蔽", ANTI_DEBUG_TAG);
    pr_info("[%s] ✅ TracerPid 显示已伪造为 0", ANTI_DEBUG_TAG);
}

// 清理反调试 Hook - 简化版
void cleanup_anti_debug_hooks(void) {
    pr_info("[%s] 卸载通杀反调试模块...", ANTI_DEBUG_TAG);
    
    fp_unhook_syscalln(__NR_ptrace, before_ptrace_hook, 0);
    fp_unhook_syscalln(__NR_read, 0, after_read_antidebug);
    
    pr_info("[%s] 统计 - ptrace阻止:%llu TracerPid伪造:%llu", 
            ANTI_DEBUG_TAG, ptrace_block_count, status_modify_count);
}
