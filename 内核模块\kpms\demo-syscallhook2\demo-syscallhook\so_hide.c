/* SPDX-License-Identifier: GPL-2.0-or-later */
/*
 * SO 隐藏模块
 * 作用：在 /proc/<pid>/maps 输出中过滤指定 so 路径，达到“隐形”效果。
 * 只在内核态修改拷贝到用户空间的缓冲区，文件本身不变。
 */

#include <compiler.h>
#include <kpmodule.h>
#include <linux/printk.h>
#include <uapi/asm-generic/unistd.h>
#include <linux/uaccess.h>
#include <syscall.h>
#include <linux/string.h>
#include <kputils.h>

#define SO_HIDE_TAG "SoHide"
/* 需要隐藏的 so 路径关键词 */
static const char *TARGET_SO_KEY = "/data/local/tmp/libUE5.so";

/*
 * 对 maps 读取内容进行就地过滤：
 * 1. 找到包含 TARGET_SO_KEY 的行。
 * 2. 通过 memmove 把后续内容向前挪动覆盖。
 * 3. 更新 new_size 返回值。
 */
void process_maps_content(int fd, char __user *buf, ssize_t bytes_read, ssize_t *new_size)
{
    if (bytes_read <= 0 || bytes_read > 4096) {
        *new_size = bytes_read;
        return;
    }

    char kbuf[4097];
    if (compat_strncpy_from_user(kbuf, buf, bytes_read) < 0) {
        *new_size = bytes_read;
        return;
    }
    kbuf[bytes_read] = '\0';

    int write_pos = 0;
    char *cur = kbuf;
    while (cur < kbuf + bytes_read) {
        char *line_end = strchr(cur, '\n');
        if (!line_end) break;
        size_t line_len = line_end - cur + 1; // 包含换行

        if (strstr(cur, TARGET_SO_KEY) == NULL) {
            /* 保留该行 */
            if (write_pos != (cur - kbuf)) {
                memmove(kbuf + write_pos, cur, line_len);
            }
            write_pos += line_len;
        }
        // 移动到下一行
        cur = line_end + 1;
    }

    if (write_pos < bytes_read) {
        /* 将过滤后的内容写回用户空间 */
        compat_copy_to_user(buf, kbuf, write_pos);
        *new_size = write_pos;
        pr_info("[%s] 已隐藏 maps 中的 %s，原:%ld -> 新:%d", SO_HIDE_TAG, TARGET_SO_KEY, (long)bytes_read, write_pos);
    } else {
        *new_size = bytes_read;
    }
}

/* ------------------------- syscall hook ------------------------- */

#define MAX_TRACK_FD 16
static int tracked_fd[MAX_TRACK_FD];
static int tracked_cnt = 0;

static void track_fd_add(int fd) {
    if (tracked_cnt >= MAX_TRACK_FD) return;
    tracked_fd[tracked_cnt++] = fd;
}
static bool is_tracked_fd(int fd) {
    for (int i = 0; i < tracked_cnt; i++) if (tracked_fd[i] == fd) return true;
    return false;
}
static void track_fd_remove(int fd) {
    for (int i = 0; i < tracked_cnt; i++) {
        if (tracked_fd[i] == fd) {
            tracked_fd[i] = tracked_fd[--tracked_cnt];
            return;
        }
    }
}

// openat before: 记录 /proc/*/maps 打开的 fd
void before_openat_sohide(hook_fargs4_t *args, void *udata)
{
    const char __user *pathname = (const char __user *)syscall_argn(args, 1);
    char path[128];
    if (compat_strncpy_from_user(path, pathname, sizeof(path) - 1) <= 0)
        return;
    path[sizeof(path) - 1] = '\0';
    if (strstr(path, "/maps")) {
        args->local.data0 = 1; // 标记
        pr_info("[%s] 监控 maps 打开: %s", SO_HIDE_TAG, path);
    }
}

void after_openat_sohide(hook_fargs4_t *args, void *udata)
{
    if (args->local.data0 == 1 && args->ret >= 0) {
        track_fd_add((int)args->ret);
    }
}

void after_read_sohide(hook_fargs3_t *args, void *udata)
{
    int fd = (int)syscall_argn(args, 0);
    if (!is_tracked_fd(fd)) return;

    char __user *buf = (char __user *)syscall_argn(args, 1);
    ssize_t bytes_read = (ssize_t)args->ret;
    ssize_t new_size = bytes_read;
    process_maps_content(fd, buf, bytes_read, &new_size);
    if (new_size != bytes_read)
        args->ret = new_size;
}

void before_close_sohide(hook_fargs1_t *args, void *udata)
{
    int fd = (int)syscall_argn(args, 0);
    if (is_tracked_fd(fd))
        track_fd_remove(fd);
}

/* 初始化 */
void init_so_hide_hooks(void)
{
    hook_err_t err;
    pr_info("[%s] SO 隐藏模块加载...", SO_HIDE_TAG);

    err = fp_hook_syscalln(__NR_openat, 4, before_openat_sohide, after_openat_sohide, 0);
    if (err) { pr_err("[%s] hook openat fail:%d", SO_HIDE_TAG, err); return; }

    err = fp_hook_syscalln(__NR_read, 3, 0, after_read_sohide, 0);
    if (err) { pr_err("[%s] hook read fail:%d", SO_HIDE_TAG, err); return; }

    err = fp_hook_syscalln(__NR_close, 1, before_close_sohide, 0, 0);
    if (err) { pr_err("[%s] hook close fail:%d", SO_HIDE_TAG, err); return; }

    pr_info("[%s] SO 隐藏模块已就绪，目标关键词: %s", SO_HIDE_TAG, TARGET_SO_KEY);
}

void cleanup_so_hide_hooks(void)
{
    pr_info("[%s] 卸载 SO 隐藏模块...", SO_HIDE_TAG);
    fp_unhook_syscalln(__NR_openat, before_openat_sohide, after_openat_sohide);
    fp_unhook_syscalln(__NR_read, 0, after_read_sohide);
    fp_unhook_syscalln(__NR_close, before_close_sohide, 0);
    tracked_cnt = 0;
}
