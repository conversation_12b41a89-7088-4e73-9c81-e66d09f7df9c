/* SPDX-License-Identifier: GPL-2.0-or-later */
/*
 * SO 隐藏模块2 - UID 精准隐藏版本
 * 作用：在 /proc/<pid>/maps 输出中基于 UID 过滤指定 so 路径，达到"隐形"效果。
 * 只在内核态修改拷贝到用户空间的缓冲区，文件本身不变。
 * 支持针对特定 UID 隐藏，避免全局影响调试。
 */

#include <compiler.h>
#include <kpmodule.h>
#include <linux/printk.h>
#include <uapi/asm-generic/unistd.h>
#include <linux/uaccess.h>
#include <syscall.h>
#include <linux/string.h>
#include <kputils.h>
#include <kallsyms.h>

/* 函数原型声明，避免隐式声明错误 */
static inline bool contains_case_insensitive(const char *haystack, const char *needle);
static inline char ascii_tolower_char(char c);

/* ASCII 小写转换，避免依赖 ctype.h */
static inline char ascii_tolower_char(char c) {
    if (c >= 'A' && c <= 'Z') return c + ('a' - 'A');
    return c;
}

#define SO_HIDE2_TAG "SoHide2"
#define MAX_HIDE_RULES 10
#define MAX_EXCLUDE_RULES 10  // 新增：最大排除规则数量
#define PATH_MAX 256

/* UID类型定义（在KMP环境中使用基本类型）*/
typedef unsigned int uid_t;

/* SO隐藏规则结构体 */
struct so_hide_rule {
    uid_t target_uid;           // 目标UID，0表示全局
    char so_keyword[PATH_MAX];  // 要隐藏的SO关键词
};

/* SO排除规则结构体 */
struct so_exclude_rule {
    uid_t target_uid;           // 目标UID，0表示全局
    char so_keyword[PATH_MAX];  // 要排除的SO关键词（不隐藏）
};

/* SO隐藏规则数组 */
static struct so_hide_rule hide_rules[MAX_HIDE_RULES];
static int rule_count = 0;

/* SO排除规则数组 */
static struct so_exclude_rule exclude_rules[MAX_EXCLUDE_RULES];
static int exclude_count = 0;

/* 获取UID的系统调用函数指针 */
static uid_t (*sys_getuid)(void) = NULL;

/* 获取当前进程的UID */
static inline uid_t get_current_uid(void) {
    if (sys_getuid) {
        return sys_getuid();
    }
    // 备用方案：返回固定值用于测试
    return 12345;
}

/* 检查当前UID是否需要隐藏SO */
static inline bool should_hide_for_current_uid(uid_t current_uid) {
    for (int i = 0; i < rule_count; i++) {
        // 如果规则UID为0，表示全局规则，匹配所有UID
        if (hide_rules[i].target_uid == 0) {
            return true;
        }
        // 否则只有UID完全匹配才生效
        if (current_uid == hide_rules[i].target_uid) {
            return true;
        }
    }
    return false;
}

/* 检查当前UID是否需要排除某个SO */
static inline bool should_exclude_for_current_uid(uid_t current_uid, const char *line) {
    for (int i = 0; i < exclude_count; i++) {
        // 如果排除规则UID为0，表示全局规则，匹配所有UID
        if (exclude_rules[i].target_uid == 0 || current_uid == exclude_rules[i].target_uid) {
            if (contains_case_insensitive(line, exclude_rules[i].so_keyword)) {
                return true;  // 找到排除规则，不应该隐藏
            }
        }
    }
    return false;  // 没有找到排除规则
}

/* 检查某行是否包含需要隐藏的SO（考虑排除规则） */
static inline bool line_contains_target_so(const char *line, uid_t current_uid) {
    // 首先检查是否在排除列表中
    if (should_exclude_for_current_uid(current_uid, line)) {
        return false;  // 在排除列表中，不隐藏
    }
    
    // 检查是否在隐藏列表中
    for (int i = 0; i < rule_count; i++) {
        // 只检查与当前UID匹配的隐藏规则
        if (hide_rules[i].target_uid == 0 || current_uid == hide_rules[i].target_uid) {
            if (contains_case_insensitive(line, hide_rules[i].so_keyword)) {
                return true;  // 需要隐藏
            }
        }
    }
    return false;  // 不需要隐藏
}

/*
 * 对 maps 读取内容进行基于UID的过滤：
 * 1. 检查当前进程UID是否在隐藏规则中。
 * 2. 如果需要隐藏，找到包含目标SO的行并删除。
 * 3. 通过 memmove 把后续内容向前挪动覆盖。
 * 4. 更新 new_size 返回值。
 */
void process_maps_content2(int fd, char __user *buf, ssize_t bytes_read, ssize_t *new_size)
{
    if (bytes_read <= 0 || bytes_read > 4096) {
        *new_size = bytes_read;
        return;
    }

    // 获取当前进程UID
    uid_t current_uid = get_current_uid();
    
    // 检查是否需要为当前UID隐藏SO
    if (!should_hide_for_current_uid(current_uid)) {
        *new_size = bytes_read;
        return;
    }

    char kbuf[4097];
    if (compat_strncpy_from_user(kbuf, buf, bytes_read) < 0) {
        *new_size = bytes_read;
        return;
    }
    kbuf[bytes_read] = '\0';

    int write_pos = 0;
    int hidden_lines = 0;
    char *cur = kbuf;
    
    while (cur < kbuf + bytes_read) {
        char *line_end = strchr(cur, '\n');
        if (!line_end) break;
        size_t line_len = line_end - cur + 1; // 包含换行

        // 仅在当前行范围内进行匹配，避免跨行误匹配
        char saved_ch = *line_end;
        *line_end = '\0';
        bool match_hide = line_contains_target_so(cur, current_uid);
        *line_end = saved_ch;

        // 检查当前行是否包含需要隐藏的SO
        if (!match_hide) {
            /* 保留该行 */
            if (write_pos != (cur - kbuf)) {
                memmove(kbuf + write_pos, cur, line_len);
            }
            write_pos += line_len;
        } else {
            /* 隐藏该行 */
            hidden_lines++;
        }
        // 移动到下一行
        cur = line_end + 1;
    }

    if (write_pos < bytes_read) {
        /* 将过滤后的内容写回用户空间 */
        compat_copy_to_user(buf, kbuf, write_pos);
        *new_size = write_pos;
        pr_info("[%s] UID %u: 隐藏了 %d 行SO信息，原:%ld -> 新:%d", 
                SO_HIDE2_TAG, current_uid, hidden_lines, (long)bytes_read, write_pos);
    } else {
        *new_size = bytes_read;
    }
}

/* ------------------------- syscall hook ------------------------- */

#define MAX_TRACK_FD2 16
static int tracked_fd2[MAX_TRACK_FD2];
static int tracked_cnt2 = 0;

static void track_fd_add2(int fd) {
    if (tracked_cnt2 >= MAX_TRACK_FD2) return;
    tracked_fd2[tracked_cnt2++] = fd;
}
static bool is_tracked_fd2(int fd) {
    for (int i = 0; i < tracked_cnt2; i++) if (tracked_fd2[i] == fd) return true;
    return false;
}
static void track_fd_remove2(int fd) {
    for (int i = 0; i < tracked_cnt2; i++) {
        if (tracked_fd2[i] == fd) {
            tracked_fd2[i] = tracked_fd2[--tracked_cnt2];
            return;
        }
    }
}

// openat before: 记录 /proc/*/maps 打开的 fd
void before_openat_sohide2(hook_fargs4_t *args, void *udata)
{
    const char __user *pathname = (const char __user *)syscall_argn(args, 1);
    char path[128];
    if (compat_strncpy_from_user(path, pathname, sizeof(path) - 1) <= 0)
        return;
    path[sizeof(path) - 1] = '\0';
    if (strstr(path, "/maps")) {
        args->local.data0 = 1; // 标记
        pr_info("[%s] 监控 maps 打开: %s", SO_HIDE2_TAG, path);
    }
}

void after_openat_sohide2(hook_fargs4_t *args, void *udata)
{
    if (args->local.data0 == 1 && args->ret >= 0) {
        track_fd_add2((int)args->ret);
    }
}

void after_read_sohide2(hook_fargs3_t *args, void *udata)
{
    int fd = (int)syscall_argn(args, 0);
    if (!is_tracked_fd2(fd)) return;

    char __user *buf = (char __user *)syscall_argn(args, 1);
    ssize_t bytes_read = (ssize_t)args->ret;
    ssize_t new_size = bytes_read;
    process_maps_content2(fd, buf, bytes_read, &new_size);
    if (new_size != bytes_read)
        args->ret = new_size;
}

void before_close_sohide2(hook_fargs1_t *args, void *udata)
{
    int fd = (int)syscall_argn(args, 0);
    if (is_tracked_fd2(fd))
        track_fd_remove2(fd);
}

/* 简单的字符串转整数函数 */
static long simple_strtol(const char *nptr, char **endptr, int base) {
    const char *s = nptr;
    long acc = 0;
    int neg = 0;
    
    // 跳过空白字符
    while (*s == ' ' || *s == '\t') s++;
    
    // 处理符号
    if (*s == '-') {
        neg = 1;
        s++;
    } else if (*s == '+') {
        s++;
    }
    
    // 转换数字
    while (*s >= '0' && *s <= '9') {
        acc = acc * 10 + (*s - '0');
        s++;
    }
    
    if (endptr) *endptr = (char *)s;
    return neg ? -acc : acc;
}

/* 不区分大小写的子串匹配 */
static inline bool contains_case_insensitive(const char *haystack, const char *needle)
{
    size_t needle_len;
    const char *p;
    if (!haystack || !needle || !*needle) return false;
    needle_len = strlen(needle);
    for (p = haystack; *p; p++) {
        size_t i = 0;
        while (i < needle_len && p[i] && (ascii_tolower_char(p[i]) == ascii_tolower_char(needle[i]))) {
            i++;
        }
        if (i == needle_len) return true;
        if (!p[i]) break;
    }
    return false;
}

/* 去除前后空白的工具函数 */
static inline char *skip_spaces_inline(char *s) {
    while (*s == ' ' || *s == '\t' || *s == '\r' || *s == '\n') s++;
    return s;
}

static inline void rstrip_inline(char *s) {
    size_t n = strlen(s);
    while (n > 0) {
        char c = s[n - 1];
        if (c == ' ' || c == '\t' || c == '\r' || c == '\n') {
            s[--n] = '\0';
        } else {
            break;
        }
    }
}

/* 解析SO隐藏规则：格式为 "uid1:so_keyword1,uid2:!exclude_keyword2" */
/* 规则格式说明：
 * - 隐藏规则：uid:keyword （隐藏包含keyword的行）
 * - 排除规则：uid:!keyword（不隐藏包含keyword的行，即使匹配隐藏规则）
 */
static void parse_so_hide_rules(const char *args) {
    char temp[1024];
    int len = strlen(args);
    if (len >= sizeof(temp)) len = sizeof(temp) - 1;
    
    strncpy(temp, args, len);
    temp[len] = '\0';
    
    char *token = temp;
    token = skip_spaces_inline(token);
    char *next_rule;
    
    // 按逗号分割规则
    while (token && (rule_count < MAX_HIDE_RULES || exclude_count < MAX_EXCLUDE_RULES)) {
        next_rule = strchr(token, ',');
        if (next_rule) {
            *next_rule = '\0';
            next_rule++;
            next_rule = skip_spaces_inline(next_rule);
        }
        
        // 解析单个规则：uid:so_keyword 或 uid:!exclude_keyword
        char *colon = strchr(token, ':');
        if (colon) {
            *colon = '\0';
            rstrip_inline(token);
            
            // 解析UID
            uid_t uid = (uid_t)simple_strtol(token, NULL, 10);
            char *keyword = colon + 1;
            keyword = skip_spaces_inline(keyword);
            rstrip_inline(keyword);
            
            // 检查是否是排除规则（以!开头）
            if (*keyword == '!' && exclude_count < MAX_EXCLUDE_RULES) {
                // 排除规则
                exclude_rules[exclude_count].target_uid = uid;
                strncpy(exclude_rules[exclude_count].so_keyword, keyword + 1, PATH_MAX - 1);
                exclude_rules[exclude_count].so_keyword[PATH_MAX - 1] = '\0';
                
                pr_info("[%s] Add exclude rule %d: UID=%u, SO关键词=%s", 
                        SO_HIDE2_TAG, exclude_count, exclude_rules[exclude_count].target_uid, 
                        exclude_rules[exclude_count].so_keyword);
                
                exclude_count++;
            } else if (*keyword != '!' && rule_count < MAX_HIDE_RULES) {
                // 隐藏规则
                hide_rules[rule_count].target_uid = uid;
                strncpy(hide_rules[rule_count].so_keyword, keyword, PATH_MAX - 1);
                hide_rules[rule_count].so_keyword[PATH_MAX - 1] = '\0';
                
                pr_info("[%s] Add hide rule %d: UID=%u, SO关键词=%s", 
                        SO_HIDE2_TAG, rule_count, hide_rules[rule_count].target_uid, 
                        hide_rules[rule_count].so_keyword);
                
                rule_count++;
            }
        }
        
        token = next_rule;
    }
    
    pr_info("[%s] 共加载了 %d 条SO隐藏规则和 %d 条SO排除规则", 
            SO_HIDE2_TAG, rule_count, exclude_count);
}

/* 初始化 */
void init_so_hide2_hooks(void)
{
    hook_err_t err;
    pr_info("[%s] SO 隐藏模块2加载（UID精准隐藏模式）...", SO_HIDE2_TAG);

    // 尝试获取getuid系统调用
    sys_getuid = (typeof(sys_getuid))kallsyms_lookup_name("__arm64_sys_getuid");
    if (!sys_getuid) {
        sys_getuid = (typeof(sys_getuid))kallsyms_lookup_name("sys_getuid");
        if (!sys_getuid) {
            pr_info("[%s] Warning: getuid syscall not found, using fallback UID method", SO_HIDE2_TAG);
        }
    }

    err = fp_hook_syscalln(__NR_openat, 4, before_openat_sohide2, after_openat_sohide2, 0);
    if (err) { pr_err("[%s] hook openat fail:%d", SO_HIDE2_TAG, err); return; }

    err = fp_hook_syscalln(__NR_read, 3, 0, after_read_sohide2, 0);
    if (err) { pr_err("[%s] hook read fail:%d", SO_HIDE2_TAG, err); return; }

    err = fp_hook_syscalln(__NR_close, 1, before_close_sohide2, 0, 0);
    if (err) { pr_err("[%s] hook close fail:%d", SO_HIDE2_TAG, err); return; }

    pr_info("[%s] SO 隐藏模块2已就绪，等待规则配置...", SO_HIDE2_TAG);
}

/* 控制接口 - 用于动态配置隐藏规则 */
long so_hide2_control(const char *args) {
    if (!args || !*args) {
        pr_info("[%s] 清空所有SO隐藏规则和排除规则", SO_HIDE2_TAG);
        rule_count = 0;
        exclude_count = 0;
        return 0;
    }
    
    pr_info("[%s] 配置SO隐藏规则: %s", SO_HIDE2_TAG, args);
    parse_so_hide_rules(args);
    
    return 0;
}

void cleanup_so_hide2_hooks(void)
{
    pr_info("[%s] 卸载 SO 隐藏模块2...", SO_HIDE2_TAG);
    fp_unhook_syscalln(__NR_openat, before_openat_sohide2, after_openat_sohide2);
    fp_unhook_syscalln(__NR_read, 0, after_read_sohide2);
    fp_unhook_syscalln(__NR_close, before_close_sohide2, 0);
    tracked_cnt2 = 0;
    rule_count = 0;
    exclude_count = 0;
}
