/* SPDX-License-Identifier: GPL-2.0-or-later */
/*
 * KPM 主模块入口
 * 统一管理所有子模块的加载和卸载
 */

#include <compiler.h>
#include <kpmodule.h>
#include <linux/printk.h>
#include <linux/string.h>

// KPM 模块信息
KPM_NAME("kmp-game-protection");
KPM_VERSION("2.0.0");
KPM_LICENSE("GPL v2");
KPM_AUTHOR("CustomBuild");
KPM_DESCRIPTION("Game Protection Suite - Anti-Debug, SO Hide, File Block, MinCore Hook");

#define MAIN_TAG "GameProtect"

// 子模块函数声明
extern void init_mincore_hooks(void);
extern void cleanup_mincore_hooks(void);

extern void init_so_hide_hooks(void);
extern void cleanup_so_hide_hooks(void);

extern void init_so_hide2_hooks(void);
extern void cleanup_so_hide2_hooks(void);
extern long so_hide2_control(const char *args);

extern void init_block_mrpcs_hooks(void);
extern void cleanup_block_mrpcs_hooks(void);

extern void init_anti_debug_hooks(void);
extern void cleanup_anti_debug_hooks(void);

/*
 * 模块配置 - 可以通过参数控制启用哪些功能
 */
struct module_config {
    bool enable_mincore;
    bool enable_so_hide;      // SO隐藏模块1（重命名模式）
    bool enable_so_hide2;     // SO隐藏模块2（完全删除模式）
    bool enable_file_block;
    bool enable_anti_debug;
};

static struct module_config config = {
    .enable_mincore = true,     // mincore hook（内存检测绕过）
    .enable_so_hide = false,    // SO隐藏1-重命名模式（已关闭）
    .enable_so_hide2 = true,    // SO隐藏2-UID精准隐藏模式（已启用）
    .enable_file_block = false, // 文件拦截（已关闭）
    .enable_anti_debug = false, // 反调试（默认关闭）
};

/*
 * 解析启动参数
 */
static void parse_module_args(const char *args)
{
    if (!args || !*args) {
        pr_info("[%s] 使用默认配置", MAIN_TAG);
        return;
    }
    
    pr_info("[%s] 解析参数: %s", MAIN_TAG, args);
    
    // 简单的参数解析
    if (strstr(args, "disable_mincore")) config.enable_mincore = false;
    if (strstr(args, "enable_so_hide")) config.enable_so_hide = true;
    if (strstr(args, "enable_so_hide2")) config.enable_so_hide2 = true;
    if (strstr(args, "disable_file_block")) config.enable_file_block = false;
    if (strstr(args, "enable_anti_debug")) config.enable_anti_debug = true;
    
    pr_info("[%s] 配置: mincore=%d so_hide=%d so_hide2=%d file_block=%d anti_debug=%d", 
            MAIN_TAG, config.enable_mincore, config.enable_so_hide, config.enable_so_hide2,
            config.enable_file_block, config.enable_anti_debug);
}

/*
 * 主模块初始化
 */
static long main_module_init(const char *args, const char *event, void *__user reserved)
{
    pr_info("[%s] ==========================================", MAIN_TAG);
    pr_info("[%s] 🚀 游戏保护套件启动中...", MAIN_TAG);
    pr_info("[%s] ==========================================", MAIN_TAG);
    
    // 解析参数
    parse_module_args(args);
    
    int loaded_modules = 0;
    
    // 1. 加载 MinCore Hook 模块
    if (config.enable_mincore) {
        pr_info("[%s] 📡 加载内存检测绕过模块...", MAIN_TAG);
        init_mincore_hooks();
        loaded_modules++;
    }
    
    // 2. 加载 SO 隐藏模块1（重命名模式）
    if (config.enable_so_hide) {
        pr_info("[%s] 👻 加载SO隐藏模块1（重命名模式）...", MAIN_TAG);
        init_so_hide_hooks();
        loaded_modules++;
    }
    
    // 3. 加载 SO 隐藏模块2（UID精准隐藏模式）
    if (config.enable_so_hide2) {
        pr_info("[%s] 🎯 加载SO隐藏模块2（UID精准隐藏模式）...", MAIN_TAG);
        init_so_hide2_hooks();
        loaded_modules++;
    }
    
    // 4. 加载文件拦截模块
    if (config.enable_file_block) {
        pr_info("[%s] 🛡️ 加载文件拦截模块...", MAIN_TAG);
        init_block_mrpcs_hooks();
        loaded_modules++;
    }
    
    // 5. 加载反调试模块（可选）
    if (config.enable_anti_debug) {
        pr_info("[%s] 🔐 加载反调试模块...", MAIN_TAG);
        init_anti_debug_hooks();
        loaded_modules++;
    }
    
    pr_info("[%s] ==========================================", MAIN_TAG);
    pr_info("[%s] ✅ 已加载 %d 个保护模块", MAIN_TAG, loaded_modules);
    pr_info("[%s] 🎮 游戏保护套件准备就绪！", MAIN_TAG);
    pr_info("[%s] ==========================================", MAIN_TAG);
    
    return 0;
}

/*
 * 控制接口
 */
static long main_module_control(const char *args, char *__user out_msg, int outlen)
{
    pr_info("[%s] 收到控制指令: %s", MAIN_TAG, args);
    
    // 检查是否是SO隐藏2模块的配置指令
    if (config.enable_so_hide2 && args && strncmp(args, "so_hide2:", 9) == 0) {
        // 转发给SO隐藏2模块处理
        const char *so_hide_args = args + 9; // 跳过 "so_hide2:" 前缀
        long result = so_hide2_control(so_hide_args);
        pr_info("[%s] SO隐藏2模块配置完成，结果: %ld", MAIN_TAG, result);
        return result;
    }
    
    // 可以在这里添加其他运行时控制逻辑
    // 比如动态开关某些模块等
    
    return 0;
}

/*
 * 主模块卸载
 */
static long main_module_exit(void *__user reserved)
{
    pr_info("[%s] ==========================================", MAIN_TAG);
    pr_info("[%s] 🛑 游戏保护套件卸载中...", MAIN_TAG);
    pr_info("[%s] ==========================================", MAIN_TAG);
    
    // 按相反顺序卸载模块
    if (config.enable_anti_debug) {
        pr_info("[%s] 🔓 卸载反调试模块...", MAIN_TAG);
        cleanup_anti_debug_hooks();
    }
    
    if (config.enable_file_block) {
        pr_info("[%s] 🗑️ 卸载文件拦截模块...", MAIN_TAG);
        cleanup_block_mrpcs_hooks();
    }
    
    if (config.enable_so_hide2) {
        pr_info("[%s] 🎯 卸载SO隐藏模块2（UID精准隐藏模式）...", MAIN_TAG);
        cleanup_so_hide2_hooks();
    }
    
    if (config.enable_so_hide) {
        pr_info("[%s] 👁️ 卸载SO隐藏模块1（重命名模式）...", MAIN_TAG);
        cleanup_so_hide_hooks();
    }
    
    if (config.enable_mincore) {
        pr_info("[%s] 📴 卸载内存检测绕过模块...", MAIN_TAG);
        cleanup_mincore_hooks();
    }
    
    pr_info("[%s] ==========================================", MAIN_TAG);
    pr_info("[%s] ✅ 游戏保护套件已完全卸载", MAIN_TAG);
    pr_info("[%s] ==========================================", MAIN_TAG);
    
    return 0;
}

// KPM 入口点
KPM_INIT(main_module_init);
KPM_CTL0(main_module_control);
KPM_EXIT(main_module_exit);
