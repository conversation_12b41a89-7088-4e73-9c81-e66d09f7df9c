// 提供模块内的memcpy/memset/memmove实现，避免加载时解析到内核里的弱符号W
// 防止编译器再次折叠为内置或其他符号
__attribute__((noinline, optimize("O2")))
void *memcpy(void *dest, const void *src, unsigned long n) {
    unsigned char *d = (unsigned char *)dest;
    const unsigned char *s = (const unsigned char *)src;
    for (unsigned long i = 0; i < n; ++i) d[i] = s[i];
    return dest;
}

__attribute__((noinline, optimize("O2")))
void *__memcpy(void *dest, const void *src, unsigned long n) {
    return memcpy(dest, src, n);
}

__attribute__((noinline, optimize("O2")))
void *memset(void *s, int c, unsigned long n) {
    unsigned char *p = (unsigned char *)s;
    unsigned char v = (unsigned char)c;
    for (unsigned long i = 0; i < n; ++i) p[i] = v;
    return s;
}

__attribute__((noinline, optimize("O2")))
void *__memset(void *s, int c, unsigned long n) {
    return memset(s, c, n);
}

__attribute__((noinline, optimize("O2")))
void *memmove(void *dest, const void *src, unsigned long n) {
    unsigned char *d = (unsigned char *)dest;
    const unsigned char *s = (const unsigned char *)src;
    if (d == s || n == 0) return dest;
    if (d < s) {
        for (unsigned long i = 0; i < n; ++i) d[i] = s[i];
    } else {
        for (unsigned long i = n; i > 0; --i) d[i-1] = s[i-1];
    }
    return dest;
}

__attribute__((noinline, optimize("O2")))
void *__memmove(void *dest, const void *src, unsigned long n) {
    return memmove(dest, src, n);
}

