#include <jni.h>
#include <dlfcn.h>
#include <android/log.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/mman.h>
#include <sys/syscall.h>
#include <stdarg.h>
#include <string>
#include <vector>
#include <errno.h>
#include <thread>
#include <chrono>
#include "Dobby/dobby.h"
#include <link.h>
#include <cstdint>

#define LOG_TAG "FakeMaps"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO,  LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// openat 原始指针
static int (*orig_openat)(int, const char *, int, ...) = nullptr;
// openat 递归保护
static __thread bool in_open_hook = false;
// open 原始指针
static int (*orig_open)(const char*, int, ...) = nullptr;
// 文件系统钩子是否已安装
static bool fs_hooks_installed = false;

// 库自身名称，用于在栈回溯中跳过自身
static const char* g_self_so_name = "libUE5.so";

// 获取真实调用方 SO（跳过自身和 trampoline）
static const char* find_caller_so() {
    void* ra0 = __builtin_return_address(0);
    if (ra0 && reinterpret_cast<uintptr_t>(ra0) >= 4096) {
        Dl_info info;
        if (dladdr(ra0, &info) && info.dli_fname) {
            if (!(g_self_so_name && strstr(info.dli_fname, g_self_so_name))) {
                return info.dli_fname;
            }
        }
    }
    void* ra1 = __builtin_return_address(1);
    if (ra1 && reinterpret_cast<uintptr_t>(ra1) >= 4096) {
        Dl_info info;
        if (dladdr(ra1, &info) && info.dli_fname) {
            if (!(g_self_so_name && strstr(info.dli_fname, g_self_so_name))) {
                return info.dli_fname;
            }
        }
    }
    return nullptr;
}

// 重新实现 create_custom_maps_fd，只负责隐藏指定 so
static int create_custom_maps_fd() {
    // 调用原始 openat 读取真实 maps
    int real_fd = orig_openat(AT_FDCWD, "/proc/self/maps", O_RDONLY);
    if (real_fd < 0) return real_fd;

    std::string maps_data;
    char buf[4096];
    ssize_t n;
    while ((n = read(real_fd, buf, sizeof(buf))) > 0) {
        maps_data.append(buf, static_cast<size_t>(n));
    }
    close(real_fd);
    LOGI("create_custom_maps_fd: 读取真实 maps %zu 字节", maps_data.size());
    // 删除包含敏感 so 的行
    const std::vector<std::string> hide_libs = { "libUE4.so" };
    for (const auto &lib : hide_libs) {
        while (true) {
            size_t pos = maps_data.find(lib);
            if (pos == std::string::npos) break;
            size_t line_start = maps_data.rfind('\n', pos);
            if (line_start == std::string::npos) line_start = 0; else ++line_start;
            size_t line_end = maps_data.find('\n', pos);
            if (line_end == std::string::npos) line_end = maps_data.size();
            maps_data.erase(line_start, line_end - line_start + (line_end < maps_data.size() ? 1 : 0));
        }
    }
    LOGI("create_custom_maps_fd: 处理后 maps 大小 %zu", maps_data.size());

    #ifndef MFD_CLOEXEC
    #define MFD_CLOEXEC 0x0001U
    #endif
    #ifndef SYS_memfd_create
    #ifdef __aarch64__
    #define SYS_memfd_create 279
    #else
    #define SYS_memfd_create 385
    #endif
    #endif

    int fake_fd = syscall(SYS_memfd_create, "fake_maps", MFD_CLOEXEC);
    if (fake_fd < 0) {
        LOGE("create_custom_maps_fd: memfd_create 失败 errno=%d", errno);
        return fake_fd;
    }

    write(fake_fd, maps_data.data(), maps_data.size());
    lseek(fake_fd, 0, SEEK_SET);
    return fake_fd;
}

// 更新 fake_openat 实现
extern "C" int fake_openat(int dirfd, const char *pathname, int flags, ...) {
    LOGI("fake_openat 调用, 路径: %s", pathname ? pathname : "(null)");
    if (in_open_hook) {
        va_list ap;
        va_start(ap, flags);
        mode_t mode = 0;
        if (flags & O_CREAT) mode = va_arg(ap, int);
        va_end(ap);
        return orig_openat(dirfd, pathname, flags, mode);
    }

    if (pathname && strstr(pathname, "/maps") != nullptr) {
        const char* so = find_caller_so();
        if (so && (strstr(so, "libtersafe.so") || strstr(so, "libanogs.so"))) {
            in_open_hook = true;
            int fd = create_custom_maps_fd();
            in_open_hook = false;
            if (fd >= 0) {
                LOGI("fake_openat: 返回伪造 maps fd=%d", fd);
                return fd;
            }
        }
    }

    va_list ap;
    va_start(ap, flags);
    mode_t mode = 0;
    if (flags & O_CREAT) mode = va_arg(ap, int);
    va_end(ap);
    return orig_openat(dirfd, pathname, flags, mode);
}

// 更新 fake_open 实现
extern "C" int fake_open(const char* pathname, int flags, ...) {
    LOGI("fake_open 调用, 路径: %s", pathname ? pathname : "(null)");
    va_list ap;
    va_start(ap, flags);
    mode_t mode = 0;
    if (flags & O_CREAT) mode = va_arg(ap, int);
    va_end(ap);

    if (pathname && strstr(pathname, "/maps") != nullptr) {
        const char* so = find_caller_so();
        if (so && (strstr(so, "libtersafe.so") || strstr(so, "libanogs.so"))) {
            in_open_hook = true;
            int fd = create_custom_maps_fd();
            in_open_hook = false;
            if (fd >= 0) {
                LOGI("fake_open: 返回伪造 maps fd=%d", fd);
                return fd;
            }
        }
    }

    va_start(ap, flags);
    if (flags & O_CREAT) mode = va_arg(ap, int);
    va_end(ap);
    return orig_open(pathname, flags, mode);
}

// 安装文件系统钩子
static void install_fs_hooks() {
    if (fs_hooks_installed) return;
    void *openat_sym = dlsym(RTLD_DEFAULT, "openat");
    if (openat_sym) {
        if (DobbyHook(openat_sym, (void *)fake_openat, (void **)&orig_openat) == 0) {
            LOGI("成功 Hook openat");
        } else {
            LOGE("Hook openat 失败");
        }
    }
    void *open_sym = dlsym(RTLD_DEFAULT, "open");
    if (open_sym) {
        if (DobbyHook(open_sym, (void *)fake_open, (void **)&orig_open) == 0) {
            LOGI("成功 Hook open");
        } else {
            LOGE("Hook open 失败");
        }
    }
    fs_hooks_installed = true;
}

// 简化 lib_entry，仅安装文件系统 Hook
__attribute__((constructor)) static void lib_entry() {
    LOGI("=============== FakeMaps 已注入 ===============");
    install_fs_hooks();
    LOGI("==============================================");
}
