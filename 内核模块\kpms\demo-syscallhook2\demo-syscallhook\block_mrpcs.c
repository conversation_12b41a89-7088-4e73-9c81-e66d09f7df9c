/* SPDX-License-Identifier: GPL-2.0-or-later */
/*
 * MRPCS 文件拦截模块
 * 作用：阻止游戏下发 mrpcs 相关文件到指定目录
 * 原理：Hook openat/write 系统调用，拦截匹配的文件操作
 */

#include <compiler.h>
#include <kpmodule.h>
#include <linux/printk.h>
#include <uapi/asm-generic/unistd.h>
#include <linux/uaccess.h>
#include <syscall.h>
#include <linux/string.h>
#include <kputils.h>
#include <uapi/asm-generic/fcntl.h>

#define BLOCK_TAG "BlockMrpcs"

/* 目标路径和文件关键词 */
static const char *TARGET_PATH = "/data/user/0/com.proximabeta.mf.uamo/files/ano_tmp/";
static const char *TARGET_FILE_KEY = "mrpcs";

/* 统计计数 */
static uint64_t blocked_files = 0;
static uint64_t blocked_writes = 0;

/* 跟踪被拦截的文件描述符 */
#define MAX_BLOCKED_FDS 32
static int blocked_fds[MAX_BLOCKED_FDS];
static int blocked_fd_count = 0;

static void add_blocked_fd(int fd) {
    if (blocked_fd_count < MAX_BLOCKED_FDS) {
        blocked_fds[blocked_fd_count++] = fd;
    }
}

static bool is_blocked_fd(int fd) {
    for (int i = 0; i < blocked_fd_count; i++) {
        if (blocked_fds[i] == fd) return true;
    }
    return false;
}

static void remove_blocked_fd(int fd) {
    for (int i = 0; i < blocked_fd_count; i++) {
        if (blocked_fds[i] == fd) {
            blocked_fds[i] = blocked_fds[--blocked_fd_count];
            return;
        }
    }
}

/*
 * Hook openat - 拦截文件创建
 */
void before_openat_block(hook_fargs4_t *args, void *udata)
{
    const char __user *pathname = (const char __user *)syscall_argn(args, 1);
    int flags = (int)syscall_argn(args, 2);
    char path_buf[256];
    
    // 只处理创建/写入模式的打开
    if (!(flags & (O_CREAT | O_WRONLY | O_RDWR))) {
        return;
    }
    
    if (compat_strncpy_from_user(path_buf, pathname, sizeof(path_buf) - 1) <= 0) {
        return;
    }
    path_buf[sizeof(path_buf) - 1] = '\0';
    
    // 检查是否为目标路径
    if (strstr(path_buf, TARGET_PATH) && strstr(path_buf, TARGET_FILE_KEY)) {
        pr_info("[%s] 🚫 拦截文件创建: %s", BLOCK_TAG, path_buf);
        args->skip_origin = 1;
        args->ret = -EACCES;  // 返回权限拒绝错误
        blocked_files++;
        return;
    }
    
    // 检查是否为临时文件（可能稍后重命名为mrpcs文件）
    if (strstr(path_buf, TARGET_PATH) && 
        (strstr(path_buf, ".tmp") || strstr(path_buf, ".temp"))) {
        pr_info("[%s] 🎯 监控临时文件: %s", BLOCK_TAG, path_buf);
        args->local.data0 = 1;  // 标记为需要监控的文件
    }
}

/*
 * Hook openat after - 记录需要监控的文件描述符
 */
void after_openat_block(hook_fargs4_t *args, void *udata)
{
    if (args->local.data0 == 1 && args->ret >= 0) {
        int fd = (int)args->ret;
        add_blocked_fd(fd);
        pr_info("[%s] 📝 跟踪可疑文件描述符: %d", BLOCK_TAG, fd);
    }
}

/*
 * Hook write - 拦截可疑文件的写入
 */
void before_write_block(hook_fargs3_t *args, void *udata)
{
    int fd = (int)syscall_argn(args, 0);
    size_t count = (size_t)syscall_argn(args, 2);
    
    if (is_blocked_fd(fd)) {
        pr_info("[%s] 🚫 拦截可疑文件写入: fd=%d size=%zu", BLOCK_TAG, fd, count);
        args->skip_origin = 1;
        args->ret = count;  // 伪造写入成功，但实际丢弃数据
        blocked_writes++;
    }
}

/*
 * Hook renameat2 - 拦截文件重命名为mrpcs
 */
void before_renameat2_block(hook_fargs5_t *args, void *udata)
{
    const char __user *newname = (const char __user *)syscall_argn(args, 3);
    char new_path[256];
    
    if (compat_strncpy_from_user(new_path, newname, sizeof(new_path) - 1) <= 0) {
        return;
    }
    new_path[sizeof(new_path) - 1] = '\0';
    
    // 检查重命名目标是否包含mrpcs
    if (strstr(new_path, TARGET_PATH) && strstr(new_path, TARGET_FILE_KEY)) {
        pr_info("[%s] 🚫 拦截文件重命名: %s", BLOCK_TAG, new_path);
        args->skip_origin = 1;
        args->ret = -EACCES;  // 阻止重命名
        blocked_files++;
    }
}

/*
 * Hook close - 清理文件描述符跟踪
 */
void before_close_block(hook_fargs1_t *args, void *udata)
{
    int fd = (int)syscall_argn(args, 0);
    if (is_blocked_fd(fd)) {
        remove_blocked_fd(fd);
        pr_info("[%s] 🗑️ 停止跟踪文件描述符: %d", BLOCK_TAG, fd);
    }
}

/*
 * 初始化文件拦截模块
 */
void init_block_mrpcs_hooks(void)
{
    hook_err_t err;
    
    pr_info("[%s] 📡 MRPCS 文件拦截模块启动...", BLOCK_TAG);
    pr_info("[%s] 🎯 目标路径: %s", BLOCK_TAG, TARGET_PATH);
    pr_info("[%s] 🔍 关键词: %s", BLOCK_TAG, TARGET_FILE_KEY);
    
    // Hook openat - 拦截文件创建
    err = fp_hook_syscalln(__NR_openat, 4, before_openat_block, after_openat_block, 0);
    if (err) {
        pr_err("[%s] Hook openat 失败: %d", BLOCK_TAG, err);
        return;
    }
    
    // Hook write - 拦截可疑文件写入
    err = fp_hook_syscalln(__NR_write, 3, before_write_block, 0, 0);
    if (err) {
        pr_err("[%s] Hook write 失败: %d", BLOCK_TAG, err);
        return;
    }
    
    // Hook renameat2 - 拦截文件重命名
    err = fp_hook_syscalln(__NR_renameat2, 5, before_renameat2_block, 0, 0);
    if (err) {
        pr_err("[%s] Hook renameat2 失败: %d", BLOCK_TAG, err);
        return;
    }
    
    // Hook close - 清理跟踪
    err = fp_hook_syscalln(__NR_close, 1, before_close_block, 0, 0);
    if (err) {
        pr_err("[%s] Hook close 失败: %d", BLOCK_TAG, err);
        return;
    }
    
    pr_info("[%s] ✅ 文件拦截模块加载完成", BLOCK_TAG);
    pr_info("[%s] 🛡️ 已部署反下发防护", BLOCK_TAG);
}

/*
 * 清理文件拦截模块
 */
void cleanup_block_mrpcs_hooks(void)
{
    pr_info("[%s] 🧹 卸载文件拦截模块...", BLOCK_TAG);
    
    fp_unhook_syscalln(__NR_openat, before_openat_block, after_openat_block);
    fp_unhook_syscalln(__NR_write, before_write_block, 0);
    fp_unhook_syscalln(__NR_renameat2, before_renameat2_block, 0);
    fp_unhook_syscalln(__NR_close, before_close_block, 0);
    
    blocked_fd_count = 0;
    
    pr_info("[%s] 📊 拦截统计 - 文件:%llu 写入:%llu", 
            BLOCK_TAG, blocked_files, blocked_writes);
}
