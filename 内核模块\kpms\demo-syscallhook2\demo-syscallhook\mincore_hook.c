/* SPDX-License-Identifier: GPL-2.0-or-later */
/*
 * MinCore Hook 模块
 * 功能：拦截 mincore 系统调用，伪造内存页未驻留状态
 */

#include <compiler.h>
#include <kpmodule.h>
#include <linux/printk.h>
#include <uapi/asm-generic/unistd.h>
#include <linux/uaccess.h>
#include <syscall.h>
#include <linux/string.h>
#include <kputils.h>
#include <asm/current.h>

#define MINCORE_TAG "MinCore"

// 日志控制开关（控制频繁输出的日志）
// 如需调试，改为 true 可看到每次拦截的详细信息
static bool enable_verbose_log = false;  // false=关闭详细日志，true=开启详细日志

static uint64_t mincore_counts = 0;

/*
 * MinCore Hook 前置回调 - 记录调用信息
 */
void before_mincore_hook(hook_fargs3_t *args, void *udata)
{
    unsigned long addr = (unsigned long)syscall_argn(args, 0);
    size_t length = (size_t)syscall_argn(args, 1);
    
    uint64_t *pcount = (uint64_t *)udata;
    (*pcount)++;
    
    // 保存task指针到本地数据，供后置回调使用
    args->local.data0 = (uint64_t)current;
    
    // 详细日志（频繁输出，受开关控制）
    if (enable_verbose_log) {
        pr_info("[%s] 拦截第%llu次 addr=%llx len=%zu", MINCORE_TAG, *pcount, addr, length);
    }
}

/*
 * MinCore Hook 后置回调 - 伪造内存页未驻留
 */
void after_mincore_hook(hook_fargs3_t *args, void *udata)
{
    unsigned long addr = (unsigned long)syscall_argn(args, 0);
    size_t length = (size_t)syscall_argn(args, 1);
    unsigned long vec_raw = (unsigned long)syscall_argn(args, 2);
    unsigned char __user *vec = (typeof(vec))vec_raw;

    // 计算需要清零的页面数量
    const size_t PAGE_SIZE_FIXED = 4096;
    size_t offset = (size_t)(addr & (PAGE_SIZE_FIXED - 1));
    size_t need = length + offset;
    size_t pages = (need + PAGE_SIZE_FIXED - 1) / PAGE_SIZE_FIXED;

    // 将vec缓冲区全部清零，伪造所有页面未驻留内存
    if (vec && pages > 0) {
        unsigned char zeros[256];
        memset(zeros, 0, sizeof(zeros));
        size_t remaining = pages;
        unsigned char __user *dst = vec;
        
        while (remaining > 0) {
            int chunk = (remaining > sizeof(zeros)) ? (int)sizeof(zeros) : (int)remaining;
            (void)compat_copy_to_user(dst, zeros, chunk);
            dst += chunk;
            remaining -= (size_t)chunk;
        }
    }

    // 修改返回值为 0（成功）
    args->ret = 0;
    
    // 详细日志（频繁输出，受开关控制）
    if (enable_verbose_log) {
        pr_info("[%s] 已伪造%zu个页面为未驻留状态", MINCORE_TAG, pages);
    }
}

/*
 * 初始化 MinCore Hook
 */
void init_mincore_hooks(void)
{
    hook_err_t err = HOOK_NO_ERR;
    
    pr_info("[%s] 📡 MinCore Hook 模块启动...", MINCORE_TAG);
    pr_info("[%s] Verbose Log: %s", MINCORE_TAG, enable_verbose_log ? "ON" : "OFF");
    
    // 安装 mincore 系统调用 Hook
    err = fp_hook_syscalln(__NR_mincore, 3, before_mincore_hook, after_mincore_hook, &mincore_counts);
    if (err) {
        pr_err("[%s] ❌ Hook mincore 系统调用失败：%d", MINCORE_TAG, err);
        return;
    }
    
    pr_info("[%s] ✅ MinCore Hook 模块加载完成", MINCORE_TAG);
    pr_info("[%s] ✅ 内存驻留检测已被绕过", MINCORE_TAG);
}

/*
 * 清理 MinCore Hook
 */
void cleanup_mincore_hooks(void)
{
    pr_info("[%s] 🔄 卸载 MinCore Hook 模块...", MINCORE_TAG);
    
    // 移除 mincore Hook
    fp_unhook_syscalln(__NR_mincore, before_mincore_hook, after_mincore_hook);
    
    pr_info("[%s] ✅ MinCore Hook 已卸载，总计拦截 %llu 次", MINCORE_TAG, mincore_counts);
}